#!/bin/bash

# =============================================================================
# DATABASE IMPORT SCRIPT FOR RIASHOP LOCAL V2
# =============================================================================
# This script handles importing SQL dump files into client databases
# with proper Docker integration and error handling.

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if MariaDB container is ready
wait_for_mariadb() {
    local max_attempts=30
    local attempt=1
    
    print_status "Waiting for MariaDB container to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if docker exec riashop-mariadb mysql -u root -proot123 -e "SELECT 1;" >/dev/null 2>&1; then
            print_success "MariaDB is ready!"
            return 0
        fi
        
        print_status "Attempt $attempt/$max_attempts - MariaDB not ready yet, waiting..."
        sleep 2
        ((attempt++))
    done
    
    print_error "MariaDB container is not responding after $max_attempts attempts"
    return 1
}

# Function to import SQL file
import_sql_file() {
    local sql_file="$1"
    local database_name="$2"
    local client_name="$3"
    
    print_status "Starting database import for client: $client_name"
    print_status "Database: $database_name"
    print_status "SQL file: $sql_file"
    
    # Validate inputs
    if [[ -z "$sql_file" || -z "$database_name" || -z "$client_name" ]]; then
        print_error "Missing required parameters"
        echo "Usage: import_sql_file <sql_file> <database_name> <client_name>"
        return 1
    fi
    
    # Check if SQL file exists
    if [[ ! -f "$sql_file" ]]; then
        print_error "SQL file not found: $sql_file"
        return 1
    fi
    
    # Check file size and warn if large
    local file_size=$(stat -f%z "$sql_file" 2>/dev/null || stat -c%s "$sql_file" 2>/dev/null || echo "0")
    local file_size_mb=$((file_size / 1024 / 1024))
    
    if [[ $file_size_mb -gt 100 ]]; then
        print_warning "Large SQL file detected (${file_size_mb}MB). Import may take several minutes."
    fi
    
    # Wait for MariaDB to be ready
    if ! wait_for_mariadb; then
        return 1
    fi
    
    # Create database if it doesn't exist
    print_status "Creating database: $database_name"
    if ! docker exec riashop-mariadb mysql -u root -proot123 -e "CREATE DATABASE IF NOT EXISTS \`$database_name\`; GRANT ALL PRIVILEGES ON \`$database_name\`.* TO 'riashop'@'%'; FLUSH PRIVILEGES;"; then
        print_error "Failed to create database: $database_name"
        return 1
    fi
    
    print_success "Database created successfully: $database_name"
    
    # Import SQL file
    print_status "Importing SQL file into database..."
    print_status "This may take a while for large files..."
    
    # Copy SQL file to container temporarily
    local temp_sql="/tmp/import_$(basename "$sql_file")"
    if ! docker cp "$sql_file" "riashop-mariadb:$temp_sql"; then
        print_error "Failed to copy SQL file to container"
        return 1
    fi
    
    # Prepare SQL file by filtering out problematic statements
    local filtered_sql="/tmp/filtered_$(basename "$sql_file")"
    print_status "Preparing SQL file for import..."

    # Advanced filtering for better compatibility
    print_status "Applying advanced SQL compatibility filters..."
    docker exec riashop-mariadb sh -c "
        # Create a more sophisticated filter script
        cat > /tmp/filter_sql.awk << 'EOF'
BEGIN { skip_next = 0 }
/^CREATE FUNCTION.*RETURNS  LANGUAGE SQL$/ {
    print \"-- Skipped incomplete function: \" \$0
    skip_next = 1
    next
}
/^CREATE PROCEDURE.*\(\)$/ {
    print \"-- Skipped incomplete procedure: \" \$0
    skip_next = 1
    next
}
/^;;$/ && skip_next == 1 {
    skip_next = 0
    next
}
skip_next == 1 { next }
/^DROP DATABASE/ { next }
/^CREATE DATABASE/ { next }
/^USE \`/ { next }
/^USE / { next }
/LANGUAGE SQL/ { next }
/DEFINER=/ { next }
/SQL SECURITY/ { next }
/^\/\*![0-9].*\*\/;$/ { next }
{
    gsub(/ENGINE=MyISAM/, \"ENGINE=InnoDB\")
    gsub(/ROW_FORMAT=FIXED/, \"ROW_FORMAT=DYNAMIC\")
    gsub(/ROW_FORMAT=COMPACT/, \"ROW_FORMAT=DYNAMIC\")
    print
}
EOF

        # Apply the filter
        awk -f /tmp/filter_sql.awk '$temp_sql' > '$filtered_sql'

        # Clean up the filter script
        rm -f /tmp/filter_sql.awk
    "

    # Import the filtered SQL file using root user for maximum compatibility
    print_status "Executing SQL import with root privileges..."
    if docker exec riashop-mariadb sh -c "mysql -u root -proot123 '$database_name' < '$filtered_sql'"; then
        print_success "SQL file imported successfully!"
    else
        print_warning "Root import failed, trying with riashop user..."
        if docker exec riashop-mariadb sh -c "mysql -u riashop -priashop123 '$database_name' < '$filtered_sql'"; then
            print_success "SQL file imported successfully with riashop user!"
        else
            print_error "Failed to import SQL file with both users"
            # Clean up temp files
            docker exec riashop-mariadb rm -f "$temp_sql" "$filtered_sql" 2>/dev/null || true
            return 1
        fi
    fi

    # Clean up filtered SQL file
    docker exec riashop-mariadb rm -f "$filtered_sql" 2>/dev/null || true
    
    # Clean up temporary file
    docker exec riashop-mariadb rm -f "$temp_sql" 2>/dev/null || true
    
    # Verify import by checking table count
    local table_count=$(docker exec riashop-mariadb mysql -u riashop -priashop123 -N -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='$database_name';" 2>/dev/null || echo "0")
    
    if [[ "$table_count" -gt 0 ]]; then
        print_success "Database import completed successfully!"
        print_success "Imported $table_count tables into database: $database_name"
    else
        print_warning "Import completed but no tables found. Please verify the SQL file content."
    fi
    
    return 0
}

# Main function
main() {
    if [[ $# -lt 3 ]]; then
        echo "Usage: $0 <sql_file> <database_name> <client_name>"
        echo ""
        echo "Example:"
        echo "  $0 /path/to/client.sql sardeco_db_123_Sardeco Sardeco"
        exit 1
    fi
    
    local sql_file="$1"
    local database_name="$2"
    local client_name="$3"
    
    print_status "=== Database Import Script ==="
    print_status "Client: $client_name"
    print_status "Database: $database_name"
    print_status "SQL File: $sql_file"
    print_status "=============================="
    
    if import_sql_file "$sql_file" "$database_name" "$client_name"; then
        print_success "Database import process completed successfully!"
        exit 0
    else
        print_error "Database import process failed!"
        exit 1
    fi
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
