#!/bin/bash

# Database Cleanup Script for RiaShop Local Environment
# Safely removes databases and associated client configurations
set -e

# Configuration
CLIENTS_CONFIG_DIR="./clients"
CURRENT_CLIENT_FILE="./.current-client"
BACKUP_DIR="./backups/cleanup"
LOG_FILE="./logs/cleanup-$(date +%Y%m%d_%H%M%S).log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# Initialize directories and logging
init_cleanup() {
    mkdir -p "$BACKUP_DIR"
    mkdir -p "$(dirname "$LOG_FILE")"
    log_info "Starting database cleanup at $(date)"
    log_info "Log file: $LOG_FILE"
}

# Get list of databases matching patterns
get_databases_to_remove() {
    local databases=()
    
    # Get all databases and filter by patterns
    while IFS= read -r db; do
        if [[ "$db" =~ ^(sardeco|Sardeco_db|TestClient_db|TestForce_db) ]]; then
            databases+=("$db")
        fi
    done < <(docker exec riashop-mariadb mysql -u riashop -priashop123 -e "SHOW DATABASES;" | grep -v "Database" | grep -v "information_schema")
    
    printf '%s\n' "${databases[@]}"
}

# Backup database before removal
backup_database() {
    local db_name="$1"
    local backup_file="$BACKUP_DIR/${db_name}_$(date +%Y%m%d_%H%M%S).sql"
    
    log_info "Creating backup of database: $db_name"
    
    if docker exec riashop-mariadb mysqldump -u riashop -priashop123 "$db_name" > "$backup_file" 2>/dev/null; then
        log_success "Database backup created: $backup_file"
        return 0
    else
        log_warning "Failed to backup database $db_name (may be empty)"
        return 1
    fi
}

# Remove a single database
remove_database() {
    local db_name="$1"
    local force_mode="$2"
    
    log_info "Removing database: $db_name"
    
    # Try to backup first (unless force mode)
    if [[ "$force_mode" != "true" ]]; then
        backup_database "$db_name" || true
    fi
    
    # Remove the database
    if docker exec riashop-mariadb mysql -u riashop -priashop123 -e "DROP DATABASE IF EXISTS \`$db_name\`;" 2>/dev/null; then
        log_success "Database removed: $db_name"
        return 0
    else
        log_error "Failed to remove database: $db_name"
        return 1
    fi
}

# Find and remove associated client configuration
remove_client_config() {
    local db_name="$1"
    
    # Extract client name from database name patterns
    local client_name=""
    if [[ "$db_name" =~ ^([^_]+)_db_ ]]; then
        client_name="${BASH_REMATCH[1]}"
    elif [[ "$db_name" =~ ^sardeco ]]; then
        client_name="sardeco"
    elif [[ "$db_name" =~ ^Sardeco ]]; then
        client_name="Sardeco"
    fi
    
    if [[ -n "$client_name" ]]; then
        local config_file="$CLIENTS_CONFIG_DIR/${client_name}.conf"
        if [[ -f "$config_file" ]]; then
            log_info "Removing client configuration: $config_file"
            # Backup config file first
            cp "$config_file" "$BACKUP_DIR/${client_name}.conf.backup" 2>/dev/null || true
            rm -f "$config_file"
            log_success "Client configuration removed: $client_name"
            
            # Update current client if it was the active one
            local current_client=$(cat "$CURRENT_CLIENT_FILE" 2>/dev/null || echo "")
            if [[ "$current_client" == "$client_name" ]]; then
                rm -f "$CURRENT_CLIENT_FILE"
                log_info "Cleared current client reference"
            fi
        fi
    fi
}

# Show databases that will be removed
show_databases_preview() {
    local databases=()
    readarray -t databases < <(get_databases_to_remove)
    
    if [[ ${#databases[@]} -eq 0 ]]; then
        log_info "No databases found matching the cleanup patterns"
        return 1
    fi
    
    echo
    log_warning "The following databases will be PERMANENTLY REMOVED:"
    echo
    for db in "${databases[@]}"; do
        echo "  🗑️  $db"
    done
    echo
    log_info "Total databases to remove: ${#databases[@]}"
    return 0
}

# Main cleanup function
cleanup_databases() {
    local force_mode="$1"
    local skip_backup="$2"
    
    local databases=()
    readarray -t databases < <(get_databases_to_remove)
    
    if [[ ${#databases[@]} -eq 0 ]]; then
        log_info "No databases found to clean up"
        return 0
    fi
    
    local removed_count=0
    local failed_count=0
    
    for db in "${databases[@]}"; do
        if remove_database "$db" "$skip_backup"; then
            remove_client_config "$db"
            ((removed_count++))
        else
            ((failed_count++))
        fi
    done
    
    echo
    log_success "Cleanup completed:"
    log_info "  - Databases removed: $removed_count"
    if [[ $failed_count -gt 0 ]]; then
        log_warning "  - Failed removals: $failed_count"
    fi
    
    return 0
}

# Show help
show_help() {
    cat << EOF
Database Cleanup Script for RiaShop Local Environment

Usage: $0 [OPTIONS]

Options:
    --preview           Show databases that would be removed (dry run)
    --force             Skip confirmation prompts
    --skip-backup       Skip database backups before removal
    --help              Show this help message

Patterns cleaned up:
    - sardeco* (case-insensitive)
    - Sardeco_db_*
    - TestClient_db_*
    - TestForce_db_*

Examples:
    $0 --preview                    # Show what would be removed
    $0                              # Interactive cleanup with backups
    $0 --force --skip-backup        # Force cleanup without backups

EOF
}

# Main execution
main() {
    local preview_mode=false
    local force_mode=false
    local skip_backup=false
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --preview)
                preview_mode=true
                shift
                ;;
            --force)
                force_mode=true
                shift
                ;;
            --skip-backup)
                skip_backup=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    init_cleanup
    
    # Preview mode
    if [[ "$preview_mode" == "true" ]]; then
        show_databases_preview
        exit 0
    fi
    
    # Show what will be removed
    if ! show_databases_preview; then
        exit 0
    fi
    
    # Confirmation prompt (unless force mode)
    if [[ "$force_mode" != "true" ]]; then
        echo
        read -p "⚠️  Are you sure you want to proceed? This action cannot be undone! (yes/no): " confirm
        if [[ "$confirm" != "yes" ]]; then
            log_info "Cleanup cancelled by user"
            exit 0
        fi
    fi
    
    # Perform cleanup
    cleanup_databases "$force_mode" "$skip_backup"
    
    log_success "Database cleanup completed successfully"
}

# Run main function with all arguments
main "$@"
